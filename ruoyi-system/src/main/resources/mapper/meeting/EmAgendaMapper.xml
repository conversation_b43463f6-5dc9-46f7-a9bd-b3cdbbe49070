<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmAgendaMapper">
    
    <resultMap type="EmAgenda" id="EmAgendaResult">
        <result property="agendaId"    column="agenda_id"    />
        <result property="subMeetingId"    column="sub_meeting_id"    />
        <result property="subMeetingName"    column="sub_meeting_name"    />
        <result property="agendaTitle"    column="agenda_title"    />
        <result property="agendaContent"    column="agenda_content"    />
        <result property="presenter"    column="presenter"    />
        <result property="duration"    column="duration"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmAgendaVo">
        select a.agenda_id, a.sub_meeting_id, s.sub_meeting_name, a.agenda_title, 
               a.agenda_content, a.presenter, a.duration, a.order_num, a.status, 
               a.create_by, a.create_time, a.update_by, a.update_time, a.remark
        from em_agenda a
        left join em_sub_meeting s on a.sub_meeting_id = s.sub_meeting_id
    </sql>

    <select id="selectEmAgendaList" parameterType="EmAgenda" resultMap="EmAgendaResult">
        <include refid="selectEmAgendaVo"/>
        <where>  
            <if test="subMeetingId != null "> and a.sub_meeting_id = #{subMeetingId}</if>
            <if test="agendaTitle != null  and agendaTitle != ''"> and a.agenda_title like concat('%', #{agendaTitle}, '%')</if>
            <if test="presenter != null  and presenter != ''"> and a.presenter like concat('%', #{presenter}, '%')</if>
            <if test="status != null  and status != ''"> and a.status = #{status}</if>
        </where>
        order by a.sub_meeting_id, a.order_num
    </select>

    <select id="selectAgendasBySubMeetingId" parameterType="Long" resultMap="EmAgendaResult">
        <include refid="selectEmAgendaVo"/>
        where a.sub_meeting_id = #{subMeetingId}
        order by a.order_num
    </select>
    
    <select id="selectEmAgendaByAgendaId" parameterType="Long" resultMap="EmAgendaResult">
        <include refid="selectEmAgendaVo"/>
        where a.agenda_id = #{agendaId}
    </select>
        
    <insert id="insertEmAgenda" parameterType="EmAgenda" useGeneratedKeys="true" keyProperty="agendaId">
        insert into em_agenda
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subMeetingId != null">sub_meeting_id,</if>
            <if test="agendaTitle != null and agendaTitle != ''">agenda_title,</if>
            <if test="agendaContent != null">agenda_content,</if>
            <if test="presenter != null">presenter,</if>
            <if test="duration != null">duration,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subMeetingId != null">#{subMeetingId},</if>
            <if test="agendaTitle != null and agendaTitle != ''">#{agendaTitle},</if>
            <if test="agendaContent != null">#{agendaContent},</if>
            <if test="presenter != null">#{presenter},</if>
            <if test="duration != null">#{duration},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmAgenda" parameterType="EmAgenda">
        update em_agenda
        <trim prefix="SET" suffixOverrides=",">
            <if test="subMeetingId != null">sub_meeting_id = #{subMeetingId},</if>
            <if test="agendaTitle != null and agendaTitle != ''">agenda_title = #{agendaTitle},</if>
            <if test="agendaContent != null">agenda_content = #{agendaContent},</if>
            <if test="presenter != null">presenter = #{presenter},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where agenda_id = #{agendaId}
    </update>

    <delete id="deleteEmAgendaByAgendaId" parameterType="Long">
        delete from em_agenda where agenda_id = #{agendaId}
    </delete>

    <delete id="deleteEmAgendaByAgendaIds" parameterType="String">
        delete from em_agenda where agenda_id in 
        <foreach item="agendaId" collection="array" open="(" separator="," close=")">
            #{agendaId}
        </foreach>
    </delete>

    <delete id="deleteAgendasBySubMeetingId" parameterType="Long">
        delete from em_agenda where sub_meeting_id = #{subMeetingId}
    </delete>

    <update id="updateAgendaOrder">
        update em_agenda set order_num = #{orderNum}, update_time = sysdate() where agenda_id = #{agendaId}
    </update>

    <update id="batchUpdateAgendaOrder" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update em_agenda set order_num = #{item.orderNum}, update_time = sysdate() where agenda_id = #{item.agendaId}
        </foreach>
    </update>

    <update id="updateAgendaStatus">
        update em_agenda set status = #{status}, update_time = sysdate() where agenda_id = #{agendaId}
    </update>

    <select id="selectMaxOrderNumBySubMeetingId" parameterType="Long" resultType="Integer">
        select IFNULL(max(order_num), 0) from em_agenda where sub_meeting_id = #{subMeetingId}
    </select>

</mapper>
