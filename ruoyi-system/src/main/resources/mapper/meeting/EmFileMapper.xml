<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmFileMapper">
    
    <resultMap type="EmFile" id="EmFileResult">
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalName"    column="original_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileExt"    column="file_ext"    />
        <result property="agendaId"    column="agenda_id"    />
        <result property="agendaTitle"    column="agenda_title"    />
        <result property="meetingId"    column="meeting_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="isPassword"    column="is_password"    />
        <result property="password"    column="password"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="EmFileDistribution" id="EmFileDistributionResult">
        <result property="distributionId"    column="distribution_id"    />
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="distributionTime"    column="distribution_time"    />
        <result property="readTime"    column="read_time"    />
        <result property="downloadTime"    column="download_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmFileVo">
        select f.file_id, f.file_name, f.original_name, f.file_path, f.file_size, 
               f.file_type, f.file_ext, f.agenda_id, a.agenda_title, f.meeting_id, 
               f.order_num, f.is_password, f.password, f.status, f.create_by, 
               f.create_time, f.update_by, f.update_time, f.remark
        from em_file f
        left join em_agenda a on f.agenda_id = a.agenda_id
    </sql>

    <select id="selectEmFileList" parameterType="EmFile" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        <where>  
            <if test="fileName != null  and fileName != ''"> and f.file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalName != null  and originalName != ''"> and f.original_name like concat('%', #{originalName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and f.file_type = #{fileType}</if>
            <if test="agendaId != null "> and f.agenda_id = #{agendaId}</if>
            <if test="meetingId != null "> and f.meeting_id = #{meetingId}</if>
            <if test="status != null  and status != ''"> and f.status = #{status}</if>
        </where>
        order by f.agenda_id, f.order_num
    </select>

    <select id="selectFilesByAgendaId" parameterType="Long" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        where f.agenda_id = #{agendaId}
        order by f.order_num
    </select>

    <select id="selectFilesByMeetingId" parameterType="Long" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        where f.meeting_id = #{meetingId}
        order by f.agenda_id, f.order_num
    </select>
    
    <select id="selectEmFileByFileId" parameterType="Long" resultMap="EmFileResult">
        <include refid="selectEmFileVo"/>
        where f.file_id = #{fileId}
    </select>
        
    <insert id="insertEmFile" parameterType="EmFile" useGeneratedKeys="true" keyProperty="fileId">
        insert into em_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalName != null and originalName != ''">original_name,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileExt != null">file_ext,</if>
            <if test="agendaId != null">agenda_id,</if>
            <if test="meetingId != null">meeting_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="isPassword != null">is_password,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalName != null and originalName != ''">#{originalName},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileExt != null">#{fileExt},</if>
            <if test="agendaId != null">#{agendaId},</if>
            <if test="meetingId != null">#{meetingId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="isPassword != null">#{isPassword},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmFile" parameterType="EmFile">
        update em_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalName != null and originalName != ''">original_name = #{originalName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileExt != null">file_ext = #{fileExt},</if>
            <if test="agendaId != null">agenda_id = #{agendaId},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="isPassword != null">is_password = #{isPassword},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteEmFileByFileId" parameterType="Long">
        delete from em_file where file_id = #{fileId}
    </delete>

    <delete id="deleteEmFileByFileIds" parameterType="String">
        delete from em_file where file_id in 
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>

    <delete id="deleteFilesByAgendaId" parameterType="Long">
        delete from em_file where agenda_id = #{agendaId}
    </delete>

    <update id="updateFileOrder">
        update em_file set order_num = #{orderNum}, update_time = sysdate() where file_id = #{fileId}
    </update>

    <update id="batchUpdateFileOrder" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update em_file set order_num = #{item.orderNum}, update_time = sysdate() where file_id = #{item.fileId}
        </foreach>
    </update>

    <select id="selectMaxOrderNumByAgendaId" parameterType="Long" resultType="Integer">
        select IFNULL(max(order_num), 0) from em_file where agenda_id = #{agendaId}
    </select>

    <!-- 文件分发相关操作 -->
    <select id="selectFileDistributions" parameterType="Long" resultMap="EmFileDistributionResult">
        select d.distribution_id, d.file_id, f.file_name, d.user_id, d.user_name, d.nick_name,
               d.distribution_time, d.read_time, d.download_time, d.status, 
               d.create_by, d.create_time, d.update_by, d.update_time, d.remark
        from em_file_distribution d
        left join em_file f on d.file_id = f.file_id
        where d.file_id = #{fileId}
        order by d.distribution_time desc
    </select>

    <insert id="insertFileDistribution" parameterType="EmFileDistribution" useGeneratedKeys="true" keyProperty="distributionId">
        insert into em_file_distribution(file_id, user_id, user_name, nick_name, distribution_time, status, create_by, create_time, remark)
        values (#{fileId}, #{userId}, #{userName}, #{nickName}, #{distributionTime}, #{status}, #{createBy}, #{createTime}, #{remark})
    </insert>

    <insert id="batchInsertFileDistributions" parameterType="java.util.List">
        insert into em_file_distribution(file_id, user_id, user_name, nick_name, distribution_time, status, create_by, create_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.fileId}, #{item.userId}, #{item.userName}, #{item.nickName}, #{item.distributionTime}, #{item.status}, #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <delete id="deleteFileDistribution">
        delete from em_file_distribution where file_id = #{fileId} and user_id = #{userId}
    </delete>

    <delete id="batchDeleteFileDistributions">
        delete from em_file_distribution where file_id = #{fileId} and user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <delete id="deleteFileDistributionsByFileId" parameterType="Long">
        delete from em_file_distribution where file_id = #{fileId}
    </delete>

    <update id="updateFileDistributionStatus">
        update em_file_distribution set status = #{status}, update_time = sysdate() where distribution_id = #{distributionId}
    </update>

</mapper>
