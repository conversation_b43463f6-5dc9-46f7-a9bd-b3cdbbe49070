<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.meeting.mapper.EmMeetingMapper">
    
    <resultMap type="EmMeeting" id="EmMeetingResult">
        <result property="meetingId"    column="meeting_id"    />
        <result property="meetingName"    column="meeting_name"    />
        <result property="meetingDesc"    column="meeting_desc"    />
        <result property="typeId"    column="type_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="location"    column="location"    />
        <result property="organizer"    column="organizer"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="EmMeetingParticipant" id="EmMeetingParticipantResult">
        <result property="participantId"    column="participant_id"    />
        <result property="meetingId"    column="meeting_id"    />
        <result property="subMeetingId"    column="sub_meeting_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="roleType"    column="role_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEmMeetingVo">
        select m.meeting_id, m.meeting_name, m.meeting_desc, m.type_id, t.type_name, 
               m.start_time, m.end_time, m.location, m.organizer, m.status, 
               m.create_by, m.create_time, m.update_by, m.update_time, m.remark
        from em_meeting m
        left join em_meeting_type t on m.type_id = t.type_id
    </sql>

    <select id="selectEmMeetingList" parameterType="EmMeeting" resultMap="EmMeetingResult">
        <include refid="selectEmMeetingVo"/>
        <where>  
            <if test="meetingName != null  and meetingName != ''"> and m.meeting_name like concat('%', #{meetingName}, '%')</if>
            <if test="typeId != null "> and m.type_id = #{typeId}</if>
            <if test="status != null  and status != ''"> and m.status = #{status}</if>
            <if test="organizer != null  and organizer != ''"> and m.organizer like concat('%', #{organizer}, '%')</if>
            <if test="location != null  and location != ''"> and m.location like concat('%', #{location}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(m.start_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(m.start_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by m.create_time desc
    </select>
    
    <select id="selectEmMeetingByMeetingId" parameterType="Long" resultMap="EmMeetingResult">
        <include refid="selectEmMeetingVo"/>
        where m.meeting_id = #{meetingId}
    </select>
        
    <insert id="insertEmMeeting" parameterType="EmMeeting" useGeneratedKeys="true" keyProperty="meetingId">
        insert into em_meeting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="meetingName != null and meetingName != ''">meeting_name,</if>
            <if test="meetingDesc != null">meeting_desc,</if>
            <if test="typeId != null">type_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null">location,</if>
            <if test="organizer != null">organizer,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="meetingName != null and meetingName != ''">#{meetingName},</if>
            <if test="meetingDesc != null">#{meetingDesc},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null">#{location},</if>
            <if test="organizer != null">#{organizer},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEmMeeting" parameterType="EmMeeting">
        update em_meeting
        <trim prefix="SET" suffixOverrides=",">
            <if test="meetingName != null and meetingName != ''">meeting_name = #{meetingName},</if>
            <if test="meetingDesc != null">meeting_desc = #{meetingDesc},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null">location = #{location},</if>
            <if test="organizer != null">organizer = #{organizer},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where meeting_id = #{meetingId}
    </update>

    <delete id="deleteEmMeetingByMeetingId" parameterType="Long">
        delete from em_meeting where meeting_id = #{meetingId}
    </delete>

    <delete id="deleteEmMeetingByMeetingIds" parameterType="String">
        delete from em_meeting where meeting_id in 
        <foreach item="meetingId" collection="array" open="(" separator="," close=")">
            #{meetingId}
        </foreach>
    </delete>

    <!-- 会议参与人员相关操作 -->
    <select id="selectMeetingParticipants" parameterType="Long" resultMap="EmMeetingParticipantResult">
        select participant_id, meeting_id, sub_meeting_id, user_id, user_name, nick_name, 
               role_type, status, create_by, create_time, update_by, update_time, remark
        from em_meeting_participant 
        where meeting_id = #{meetingId}
        order by create_time
    </select>

    <insert id="batchInsertMeetingParticipants" parameterType="java.util.List">
        insert into em_meeting_participant(meeting_id, sub_meeting_id, user_id, user_name, nick_name, role_type, status, create_by, create_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.meetingId}, #{item.subMeetingId}, #{item.userId}, #{item.userName}, #{item.nickName}, #{item.roleType}, #{item.status}, #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <delete id="deleteMeetingParticipantsByMeetingId" parameterType="Long">
        delete from em_meeting_participant where meeting_id = #{meetingId}
    </delete>

    <delete id="deleteMeetingParticipantsByUserIds">
        delete from em_meeting_participant where meeting_id = #{meetingId} and user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="updateMeetingStatus">
        update em_meeting set status = #{status}, update_time = sysdate() where meeting_id = #{meetingId}
    </update>

    <select id="selectMeetingsByUserId" parameterType="Long" resultMap="EmMeetingResult">
        <include refid="selectEmMeetingVo"/>
        where m.meeting_id in (
            select distinct meeting_id from em_meeting_participant where user_id = #{userId}
        )
        order by m.start_time desc
    </select>

    <select id="selectMeetingStatistics" resultMap="EmMeetingResult">
        <include refid="selectEmMeetingVo"/>
        where m.create_time >= date_sub(curdate(), interval 30 day)
        order by m.create_time desc
        limit 10
    </select>

</mapper>
