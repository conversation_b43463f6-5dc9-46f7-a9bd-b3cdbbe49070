package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmFile;
import com.ruoyi.meeting.domain.EmFileDistribution;

/**
 * 文件管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmFileMapper 
{
    /**
     * 查询文件
     * 
     * @param fileId 文件主键
     * @return 文件
     */
    public EmFile selectEmFileByFileId(Long fileId);

    /**
     * 查询文件列表
     * 
     * @param emFile 文件
     * @return 文件集合
     */
    public List<EmFile> selectEmFileList(EmFile emFile);

    /**
     * 根据议题ID查询文件列表
     * 
     * @param agendaId 议题ID
     * @return 文件集合
     */
    public List<EmFile> selectFilesByAgendaId(Long agendaId);

    /**
     * 根据会议ID查询文件列表
     * 
     * @param meetingId 会议ID
     * @return 文件集合
     */
    public List<EmFile> selectFilesByMeetingId(Long meetingId);

    /**
     * 新增文件
     * 
     * @param emFile 文件
     * @return 结果
     */
    public int insertEmFile(EmFile emFile);

    /**
     * 修改文件
     * 
     * @param emFile 文件
     * @return 结果
     */
    public int updateEmFile(EmFile emFile);

    /**
     * 删除文件
     * 
     * @param fileId 文件主键
     * @return 结果
     */
    public int deleteEmFileByFileId(Long fileId);

    /**
     * 批量删除文件
     * 
     * @param fileIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmFileByFileIds(Long[] fileIds);

    /**
     * 根据议题ID删除文件
     * 
     * @param agendaId 议题ID
     * @return 结果
     */
    public int deleteFilesByAgendaId(Long agendaId);

    /**
     * 更新文件排序
     * 
     * @param fileId 文件ID
     * @param orderNum 排序号
     * @return 结果
     */
    public int updateFileOrder(Long fileId, Integer orderNum);

    /**
     * 批量更新文件排序
     * 
     * @param files 文件列表
     * @return 结果
     */
    public int batchUpdateFileOrder(List<EmFile> files);

    /**
     * 查询文件的最大排序号
     * 
     * @param agendaId 议题ID
     * @return 最大排序号
     */
    public Integer selectMaxOrderNumByAgendaId(Long agendaId);

    /**
     * 查询文件分发记录
     * 
     * @param fileId 文件ID
     * @return 分发记录列表
     */
    public List<EmFileDistribution> selectFileDistributions(Long fileId);

    /**
     * 新增文件分发记录
     * 
     * @param distribution 分发记录
     * @return 结果
     */
    public int insertFileDistribution(EmFileDistribution distribution);

    /**
     * 批量新增文件分发记录
     * 
     * @param distributions 分发记录列表
     * @return 结果
     */
    public int batchInsertFileDistributions(List<EmFileDistribution> distributions);

    /**
     * 删除文件分发记录
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteFileDistribution(Long fileId, Long userId);

    /**
     * 批量删除文件分发记录
     * 
     * @param fileId 文件ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int batchDeleteFileDistributions(Long fileId, Long[] userIds);

    /**
     * 删除文件的所有分发记录
     * 
     * @param fileId 文件ID
     * @return 结果
     */
    public int deleteFileDistributionsByFileId(Long fileId);

    /**
     * 更新文件分发状态
     * 
     * @param distributionId 分发记录ID
     * @param status 状态
     * @return 结果
     */
    public int updateFileDistributionStatus(Long distributionId, String status);
}
