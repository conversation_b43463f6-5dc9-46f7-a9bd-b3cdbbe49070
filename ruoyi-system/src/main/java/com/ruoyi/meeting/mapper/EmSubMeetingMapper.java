package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmSubMeeting;

/**
 * 子会议Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmSubMeetingMapper 
{
    /**
     * 查询子会议
     * 
     * @param subMeetingId 子会议主键
     * @return 子会议
     */
    public EmSubMeeting selectEmSubMeetingBySubMeetingId(Long subMeetingId);

    /**
     * 查询子会议列表
     * 
     * @param emSubMeeting 子会议
     * @return 子会议集合
     */
    public List<EmSubMeeting> selectEmSubMeetingList(EmSubMeeting emSubMeeting);

    /**
     * 根据父会议ID查询子会议列表
     * 
     * @param parentMeetingId 父会议ID
     * @return 子会议集合
     */
    public List<EmSubMeeting> selectSubMeetingsByParentId(Long parentMeetingId);

    /**
     * 新增子会议
     * 
     * @param emSubMeeting 子会议
     * @return 结果
     */
    public int insertEmSubMeeting(EmSubMeeting emSubMeeting);

    /**
     * 修改子会议
     * 
     * @param emSubMeeting 子会议
     * @return 结果
     */
    public int updateEmSubMeeting(EmSubMeeting emSubMeeting);

    /**
     * 删除子会议
     * 
     * @param subMeetingId 子会议主键
     * @return 结果
     */
    public int deleteEmSubMeetingBySubMeetingId(Long subMeetingId);

    /**
     * 批量删除子会议
     * 
     * @param subMeetingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmSubMeetingBySubMeetingIds(Long[] subMeetingIds);

    /**
     * 根据父会议ID删除子会议
     * 
     * @param parentMeetingId 父会议ID
     * @return 结果
     */
    public int deleteSubMeetingsByParentId(Long parentMeetingId);

    /**
     * 更新子会议状态
     * 
     * @param subMeetingId 子会议ID
     * @param status 状态
     * @return 结果
     */
    public int updateSubMeetingStatus(Long subMeetingId, String status);

    /**
     * 查询用户参与的子会议列表
     * 
     * @param userId 用户ID
     * @return 子会议列表
     */
    public List<EmSubMeeting> selectSubMeetingsByUserId(Long userId);
}
