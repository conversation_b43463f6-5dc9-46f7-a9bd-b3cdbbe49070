package com.ruoyi.meeting.mapper;

import java.util.List;
import com.ruoyi.meeting.domain.EmMeeting;
import com.ruoyi.meeting.domain.EmMeetingParticipant;

/**
 * 会议管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface EmMeetingMapper 
{
    /**
     * 查询会议
     * 
     * @param meetingId 会议主键
     * @return 会议
     */
    public EmMeeting selectEmMeetingByMeetingId(Long meetingId);

    /**
     * 查询会议列表
     * 
     * @param emMeeting 会议
     * @return 会议集合
     */
    public List<EmMeeting> selectEmMeetingList(EmMeeting emMeeting);

    /**
     * 新增会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    public int insertEmMeeting(EmMeeting emMeeting);

    /**
     * 修改会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    public int updateEmMeeting(EmMeeting emMeeting);

    /**
     * 删除会议
     * 
     * @param meetingId 会议主键
     * @return 结果
     */
    public int deleteEmMeetingByMeetingId(Long meetingId);

    /**
     * 批量删除会议
     * 
     * @param meetingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmMeetingByMeetingIds(Long[] meetingIds);

    /**
     * 查询会议参与人员
     * 
     * @param meetingId 会议ID
     * @return 参与人员列表
     */
    public List<EmMeetingParticipant> selectMeetingParticipants(Long meetingId);

    /**
     * 批量新增会议参与人员
     * 
     * @param participants 参与人员列表
     * @return 结果
     */
    public int batchInsertMeetingParticipants(List<EmMeetingParticipant> participants);

    /**
     * 删除会议参与人员
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    public int deleteMeetingParticipantsByMeetingId(Long meetingId);

    /**
     * 删除指定用户的会议参与记录
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int deleteMeetingParticipantsByUserIds(Long meetingId, Long[] userIds);

    /**
     * 更新会议状态
     * 
     * @param meetingId 会议ID
     * @param status 状态
     * @return 结果
     */
    public int updateMeetingStatus(Long meetingId, String status);

    /**
     * 查询用户参与的会议列表
     * 
     * @param userId 用户ID
     * @return 会议列表
     */
    public List<EmMeeting> selectMeetingsByUserId(Long userId);

    /**
     * 查询会议统计信息
     * 
     * @return 统计信息
     */
    public List<EmMeeting> selectMeetingStatistics();
}
