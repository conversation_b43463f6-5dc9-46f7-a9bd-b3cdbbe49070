package com.ruoyi.meeting.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.meeting.mapper.EmMeetingTypeMapper;
import com.ruoyi.meeting.domain.EmMeetingType;
import com.ruoyi.meeting.service.IEmMeetingTypeService;

/**
 * 会议类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class EmMeetingTypeServiceImpl implements IEmMeetingTypeService 
{
    @Autowired
    private EmMeetingTypeMapper emMeetingTypeMapper;

    /**
     * 查询会议类型
     * 
     * @param typeId 会议类型主键
     * @return 会议类型
     */
    @Override
    public EmMeetingType selectEmMeetingTypeByTypeId(Long typeId)
    {
        return emMeetingTypeMapper.selectEmMeetingTypeByTypeId(typeId);
    }

    /**
     * 查询会议类型列表
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型
     */
    @Override
    public List<EmMeetingType> selectEmMeetingTypeList(EmMeetingType emMeetingType)
    {
        return emMeetingTypeMapper.selectEmMeetingTypeList(emMeetingType);
    }

    /**
     * 查询会议类型树结构
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型树集合
     */
    @Override
    public List<EmMeetingType> selectMeetingTypeTree(EmMeetingType emMeetingType)
    {
        List<EmMeetingType> meetingTypes = emMeetingTypeMapper.selectMeetingTypeTree(emMeetingType);
        return buildMeetingTypeTree(meetingTypes);
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param meetingTypes 会议类型列表
     * @return 树结构列表
     */
    @Override
    public List<EmMeetingType> buildMeetingTypeTree(List<EmMeetingType> meetingTypes)
    {
        List<EmMeetingType> returnList = new ArrayList<EmMeetingType>();
        List<Long> tempList = new ArrayList<Long>();
        for (EmMeetingType meetingType : meetingTypes)
        {
            tempList.add(meetingType.getTypeId());
        }
        for (Iterator<EmMeetingType> iterator = meetingTypes.iterator(); iterator.hasNext();)
        {
            EmMeetingType meetingType = (EmMeetingType) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(meetingType.getParentId()))
            {
                recursionFn(meetingTypes, meetingType);
                returnList.add(meetingType);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = meetingTypes;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<EmMeetingType> list, EmMeetingType t)
    {
        // 得到子节点列表
        List<EmMeetingType> childList = getChildList(list, t);
        t.setChildren(childList);
        for (EmMeetingType tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<EmMeetingType> getChildList(List<EmMeetingType> list, EmMeetingType t)
    {
        List<EmMeetingType> tlist = new ArrayList<EmMeetingType>();
        Iterator<EmMeetingType> it = list.iterator();
        while (it.hasNext())
        {
            EmMeetingType n = (EmMeetingType) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getTypeId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<EmMeetingType> list, EmMeetingType t)
    {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param meetingTypes 会议类型列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Ztree> initZtree(List<EmMeetingType> meetingTypes)
    {
        return initZtree(meetingTypes, null, false);
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param meetingTypes 会议类型列表
     * @param roleMenuList 角色已存在菜单列表
     * @param permsFlag 是否需要显示权限标识
     * @return 下拉树结构列表
     */
    public List<Ztree> initZtree(List<EmMeetingType> meetingTypes, List<String> roleMenuList, boolean permsFlag)
    {
        List<Ztree> ztrees = new ArrayList<Ztree>();
        boolean isCheck = StringUtils.isNotNull(roleMenuList);
        for (EmMeetingType meetingType : meetingTypes)
        {
            Ztree ztree = new Ztree();
            ztree.setId(meetingType.getTypeId());
            ztree.setpId(meetingType.getParentId());
            ztree.setName(meetingType.getTypeName());
            ztree.setTitle(meetingType.getTypeName());
            if (isCheck)
            {
                ztree.setChecked(roleMenuList.contains(meetingType.getTypeId() + meetingType.getTypeName()));
            }
            ztrees.add(ztree);
        }
        return ztrees;
    }

    /**
     * 新增会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    @Override
    public int insertEmMeetingType(EmMeetingType emMeetingType)
    {
        emMeetingType.setCreateTime(DateUtils.getNowDate());
        emMeetingType.setCreateBy(SecurityUtils.getUsername());
        return emMeetingTypeMapper.insertEmMeetingType(emMeetingType);
    }

    /**
     * 修改会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    @Override
    public int updateEmMeetingType(EmMeetingType emMeetingType)
    {
        emMeetingType.setUpdateTime(DateUtils.getNowDate());
        emMeetingType.setUpdateBy(SecurityUtils.getUsername());
        return emMeetingTypeMapper.updateEmMeetingType(emMeetingType);
    }

    /**
     * 批量删除会议类型
     * 
     * @param typeIds 需要删除的会议类型主键
     * @return 结果
     */
    @Override
    public int deleteEmMeetingTypeByTypeIds(Long[] typeIds)
    {
        return emMeetingTypeMapper.deleteEmMeetingTypeByTypeIds(typeIds);
    }

    /**
     * 删除会议类型信息
     * 
     * @param typeId 会议类型主键
     * @return 结果
     */
    @Override
    public int deleteEmMeetingTypeByTypeId(Long typeId)
    {
        return emMeetingTypeMapper.deleteEmMeetingTypeByTypeId(typeId);
    }

    /**
     * 校验会议类型名称是否唯一
     * 
     * @param emMeetingType 会议类型信息
     * @return 结果
     */
    @Override
    public String checkMeetingTypeNameUnique(EmMeetingType emMeetingType)
    {
        Long typeId = StringUtils.isNull(emMeetingType.getTypeId()) ? -1L : emMeetingType.getTypeId();
        EmMeetingType info = emMeetingTypeMapper.checkMeetingTypeNameUnique(emMeetingType.getTypeName(), emMeetingType.getParentId(), typeId);
        if (StringUtils.isNotNull(info))
        {
            return "1";
        }
        return "0";
    }

    /**
     * 查询会议类型是否存在会议
     * 
     * @param typeId 会议类型ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkMeetingTypeExistMeeting(Long typeId)
    {
        int result = emMeetingTypeMapper.checkMeetingTypeExistMeeting(typeId);
        return result > 0;
    }

    /**
     * 查询会议类型是否存在子类型
     * 
     * @param typeId 会议类型ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean hasChildByTypeId(Long typeId)
    {
        int result = emMeetingTypeMapper.selectChildrenCountByParentId(typeId);
        return result > 0;
    }
}
