package com.ruoyi.meeting.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.meeting.mapper.EmMeetingMapper;
import com.ruoyi.meeting.mapper.EmSubMeetingMapper;
import com.ruoyi.meeting.domain.EmMeeting;
import com.ruoyi.meeting.domain.EmMeetingParticipant;
import com.ruoyi.meeting.service.IEmMeetingService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.domain.SysUser;

/**
 * 会议管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class EmMeetingServiceImpl implements IEmMeetingService 
{
    @Autowired
    private EmMeetingMapper emMeetingMapper;

    @Autowired
    private EmSubMeetingMapper emSubMeetingMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询会议
     * 
     * @param meetingId 会议主键
     * @return 会议
     */
    @Override
    public EmMeeting selectEmMeetingByMeetingId(Long meetingId)
    {
        EmMeeting meeting = emMeetingMapper.selectEmMeetingByMeetingId(meetingId);
        if (meeting != null) {
            // 查询参与人员
            List<EmMeetingParticipant> participants = emMeetingMapper.selectMeetingParticipants(meetingId);
            meeting.setParticipants(participants);
            
            // 查询子会议
            meeting.setSubMeetings(emSubMeetingMapper.selectSubMeetingsByParentId(meetingId));
        }
        return meeting;
    }

    /**
     * 查询会议列表
     * 
     * @param emMeeting 会议
     * @return 会议
     */
    @Override
    public List<EmMeeting> selectEmMeetingList(EmMeeting emMeeting)
    {
        return emMeetingMapper.selectEmMeetingList(emMeeting);
    }

    /**
     * 新增会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmMeeting(EmMeeting emMeeting)
    {
        emMeeting.setCreateTime(DateUtils.getNowDate());
        emMeeting.setCreateBy(SecurityUtils.getUsername());
        
        int result = emMeetingMapper.insertEmMeeting(emMeeting);
        
        // 添加参与人员
        if (emMeeting.getParticipantIds() != null && emMeeting.getParticipantIds().length > 0) {
            addParticipants(emMeeting.getMeetingId(), emMeeting.getParticipantIds());
        }
        
        return result;
    }

    /**
     * 修改会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEmMeeting(EmMeeting emMeeting)
    {
        emMeeting.setUpdateTime(DateUtils.getNowDate());
        emMeeting.setUpdateBy(SecurityUtils.getUsername());
        
        int result = emMeetingMapper.updateEmMeeting(emMeeting);
        
        // 更新参与人员
        if (emMeeting.getParticipantIds() != null) {
            setParticipants(emMeeting.getMeetingId(), emMeeting.getParticipantIds());
        }
        
        return result;
    }

    /**
     * 批量删除会议
     * 
     * @param meetingIds 需要删除的会议主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmMeetingByMeetingIds(Long[] meetingIds)
    {
        for (Long meetingId : meetingIds) {
            deleteEmMeetingByMeetingId(meetingId);
        }
        return meetingIds.length;
    }

    /**
     * 删除会议信息
     * 
     * @param meetingId 会议主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmMeetingByMeetingId(Long meetingId)
    {
        // 删除参与人员
        emMeetingMapper.deleteMeetingParticipantsByMeetingId(meetingId);
        
        // 删除子会议
        emSubMeetingMapper.deleteSubMeetingsByParentId(meetingId);
        
        // 删除会议
        return emMeetingMapper.deleteEmMeetingByMeetingId(meetingId);
    }

    /**
     * 导入会议数据
     * 
     * @param file 会议数据文件
     * @param updateSupport 是否更新支持
     * @return 结果
     */
    @Override
    @Transactional
    public String importMeeting(MultipartFile file, Boolean updateSupport) throws Exception
    {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("导入文件不能为空");
        }
        
        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".zip")) {
            throw new ServiceException("只支持zip格式的压缩包");
        }
        
        int successCount = 0;
        int failureCount = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        try (InputStream inputStream = file.getInputStream();
             ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            
            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    try {
                        // 处理压缩包中的文件
                        // 这里可以根据文件名和内容解析会议信息
                        successCount++;
                    } catch (Exception e) {
                        failureCount++;
                        failureMsg.append("<br/>").append(entry.getName()).append(" 导入失败：").append(e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            throw new ServiceException("文件解析失败：" + e.getMessage());
        }
        
        if (failureCount > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureCount + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successCount + " 条，数据如下：");
        }
        
        return successMsg.toString();
    }

    /**
     * 下载会议导入模板
     * 
     * @return 模板文件路径
     */
    @Override
    public String downloadTemplate()
    {
        // 返回模板文件路径
        return "/template/meeting_import_template.zip";
    }

    /**
     * 更新会议状态
     * 
     * @param meetingId 会议ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateMeetingStatus(Long meetingId, String status)
    {
        return emMeetingMapper.updateMeetingStatus(meetingId, status);
    }

    /**
     * 添加会议参与人员
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int addParticipants(Long meetingId, Long[] userIds)
    {
        if (userIds == null || userIds.length == 0) {
            return 0;
        }
        
        List<EmMeetingParticipant> participants = new ArrayList<>();
        for (Long userId : userIds) {
            SysUser user = sysUserMapper.selectUserById(userId);
            if (user != null) {
                EmMeetingParticipant participant = new EmMeetingParticipant();
                participant.setMeetingId(meetingId);
                participant.setUserId(userId);
                participant.setUserName(user.getUserName());
                participant.setNickName(user.getNickName());
                participant.setRoleType("0"); // 默认为参会人员
                participant.setStatus("0"); // 默认正常状态
                participant.setCreateTime(DateUtils.getNowDate());
                participant.setCreateBy(SecurityUtils.getUsername());
                participants.add(participant);
            }
        }
        
        if (!participants.isEmpty()) {
            return emMeetingMapper.batchInsertMeetingParticipants(participants);
        }
        
        return 0;
    }

    /**
     * 移除会议参与人员
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    public int removeParticipants(Long meetingId, Long[] userIds)
    {
        if (userIds == null || userIds.length == 0) {
            return 0;
        }
        
        return emMeetingMapper.deleteMeetingParticipantsByUserIds(meetingId, userIds);
    }

    /**
     * 获取会议参与人员
     * 
     * @param meetingId 会议ID
     * @return 参与人员列表
     */
    @Override
    public List<EmMeetingParticipant> getMeetingParticipants(Long meetingId)
    {
        return emMeetingMapper.selectMeetingParticipants(meetingId);
    }

    /**
     * 设置会议参与人员
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int setParticipants(Long meetingId, Long[] userIds)
    {
        // 先删除所有参与人员
        emMeetingMapper.deleteMeetingParticipantsByMeetingId(meetingId);
        
        // 再添加新的参与人员
        if (userIds != null && userIds.length > 0) {
            return addParticipants(meetingId, userIds);
        }
        
        return 0;
    }

    /**
     * 复制上次会议参与人员
     * 
     * @param meetingId 会议ID
     * @param lastMeetingId 上次会议ID
     * @return 结果
     */
    @Override
    @Transactional
    public int copyLastMeetingParticipants(Long meetingId, Long lastMeetingId)
    {
        List<EmMeetingParticipant> lastParticipants = emMeetingMapper.selectMeetingParticipants(lastMeetingId);
        if (lastParticipants != null && !lastParticipants.isEmpty()) {
            Long[] userIds = lastParticipants.stream()
                .map(EmMeetingParticipant::getUserId)
                .toArray(Long[]::new);
            return setParticipants(meetingId, userIds);
        }
        
        return 0;
    }

    /**
     * 查询用户参与的会议列表
     * 
     * @param userId 用户ID
     * @return 会议列表
     */
    @Override
    public List<EmMeeting> selectMeetingsByUserId(Long userId)
    {
        return emMeetingMapper.selectMeetingsByUserId(userId);
    }

    /**
     * 查询会议统计信息
     * 
     * @return 统计信息
     */
    @Override
    public List<EmMeeting> selectMeetingStatistics()
    {
        return emMeetingMapper.selectMeetingStatistics();
    }

    /**
     * 开始会议
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    @Override
    public int startMeeting(Long meetingId)
    {
        return updateMeetingStatus(meetingId, "1");
    }

    /**
     * 结束会议
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    @Override
    public int endMeeting(Long meetingId)
    {
        return updateMeetingStatus(meetingId, "2");
    }

    /**
     * 取消会议
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    @Override
    public int cancelMeeting(Long meetingId)
    {
        return updateMeetingStatus(meetingId, "3");
    }
}
