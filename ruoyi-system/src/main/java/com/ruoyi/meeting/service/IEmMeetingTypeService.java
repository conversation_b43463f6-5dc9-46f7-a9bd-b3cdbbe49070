package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmMeetingType;
import com.ruoyi.common.core.domain.Ztree;

/**
 * 会议类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmMeetingTypeService 
{
    /**
     * 查询会议类型
     * 
     * @param typeId 会议类型主键
     * @return 会议类型
     */
    public EmMeetingType selectEmMeetingTypeByTypeId(Long typeId);

    /**
     * 查询会议类型列表
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型集合
     */
    public List<EmMeetingType> selectEmMeetingTypeList(EmMeetingType emMeetingType);

    /**
     * 查询会议类型树结构
     * 
     * @param emMeetingType 会议类型
     * @return 会议类型树集合
     */
    public List<EmMeetingType> selectMeetingTypeTree(EmMeetingType emMeetingType);

    /**
     * 构建前端所需要树结构
     * 
     * @param meetingTypes 会议类型列表
     * @return 树结构列表
     */
    public List<EmMeetingType> buildMeetingTypeTree(List<EmMeetingType> meetingTypes);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param meetingTypes 会议类型列表
     * @return 下拉树结构列表
     */
    public List<Ztree> initZtree(List<EmMeetingType> meetingTypes);

    /**
     * 新增会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    public int insertEmMeetingType(EmMeetingType emMeetingType);

    /**
     * 修改会议类型
     * 
     * @param emMeetingType 会议类型
     * @return 结果
     */
    public int updateEmMeetingType(EmMeetingType emMeetingType);

    /**
     * 批量删除会议类型
     * 
     * @param typeIds 需要删除的会议类型主键集合
     * @return 结果
     */
    public int deleteEmMeetingTypeByTypeIds(Long[] typeIds);

    /**
     * 删除会议类型信息
     * 
     * @param typeId 会议类型主键
     * @return 结果
     */
    public int deleteEmMeetingTypeByTypeId(Long typeId);

    /**
     * 校验会议类型名称是否唯一
     * 
     * @param emMeetingType 会议类型信息
     * @return 结果
     */
    public String checkMeetingTypeNameUnique(EmMeetingType emMeetingType);

    /**
     * 查询会议类型是否存在会议
     * 
     * @param typeId 会议类型ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkMeetingTypeExistMeeting(Long typeId);

    /**
     * 查询会议类型是否存在子类型
     * 
     * @param typeId 会议类型ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean hasChildByTypeId(Long typeId);
}
