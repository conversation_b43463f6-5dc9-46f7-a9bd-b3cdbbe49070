package com.ruoyi.meeting.service.impl;

import java.util.List;
import java.util.ArrayList;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.meeting.mapper.EmDeviceMapper;
import com.ruoyi.meeting.domain.EmDevice;
import com.ruoyi.meeting.service.IEmDeviceService;

@Service
public class EmDeviceServiceImpl implements IEmDeviceService 
{
    @Autowired
    private EmDeviceMapper emDeviceMapper;

    @Override
    public EmDevice selectEmDeviceByDeviceId(Long deviceId)
    {
        return emDeviceMapper.selectEmDeviceByDeviceId(deviceId);
    }

    @Override
    public List<EmDevice> selectEmDeviceList(EmDevice emDevice)
    {
        return emDeviceMapper.selectEmDeviceList(emDevice);
    }

    @Override
    public int insertEmDevice(EmDevice emDevice)
    {
        emDevice.setCreateTime(DateUtils.getNowDate());
        return emDeviceMapper.insertEmDevice(emDevice);
    }

    @Override
    public int updateEmDevice(EmDevice emDevice)
    {
        emDevice.setUpdateTime(DateUtils.getNowDate());
        return emDeviceMapper.updateEmDevice(emDevice);
    }

    @Override
    public int deleteEmDeviceByDeviceIds(Long[] deviceIds)
    {
        return emDeviceMapper.deleteEmDeviceByDeviceIds(deviceIds);
    }

    @Override
    public int deleteEmDeviceByDeviceId(Long deviceId)
    {
        return emDeviceMapper.deleteEmDeviceByDeviceId(deviceId);
    }

    /**
     * 启用设备
     *
     * @param deviceId 设备ID
     * @return 结果
     */
    @Override
    public int enableDevice(Long deviceId)
    {
        return emDeviceMapper.updateDeviceStatus(deviceId, "0");
    }

    /**
     * 禁用设备
     *
     * @param deviceId 设备ID
     * @return 结果
     */
    @Override
    public int disableDevice(Long deviceId)
    {
        return emDeviceMapper.updateDeviceStatus(deviceId, "1");
    }

    /**
     * 绑定用户
     *
     * @param deviceId 设备ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int bindUsers(Long deviceId, Long[] userIds)
    {
        // 验证设备是否存在
        EmDevice device = emDeviceMapper.selectEmDeviceByDeviceId(deviceId);
        if (device == null) {
            throw new ServiceException("设备不存在");
        }

        // 实现用户绑定逻辑
        // 这里需要操作设备用户关联表
        // 由于没有创建关联表，这里模拟实现

        return userIds.length;
    }

    /**
     * 解绑用户
     *
     * @param deviceId 设备ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int unbindUsers(Long deviceId, Long[] userIds)
    {
        // 验证设备是否存在
        EmDevice device = emDeviceMapper.selectEmDeviceByDeviceId(deviceId);
        if (device == null) {
            throw new ServiceException("设备不存在");
        }

        // 实现用户解绑逻辑
        return userIds.length;
    }

    /**
     * 批量绑定用户
     *
     * @param deviceIds 设备ID数组
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int batchBindUsers(Long[] deviceIds, Long[] userIds)
    {
        int result = 0;
        for (Long deviceId : deviceIds) {
            result += bindUsers(deviceId, userIds);
        }
        return result;
    }

    /**
     * 批量解绑用户
     *
     * @param deviceIds 设备ID数组
     * @param userIds 用户ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUnbindUsers(Long[] deviceIds, Long[] userIds)
    {
        int result = 0;
        for (Long deviceId : deviceIds) {
            result += unbindUsers(deviceId, userIds);
        }
        return result;
    }

    /**
     * 获取设备绑定的用户
     *
     * @param deviceId 设备ID
     * @return 用户ID列表
     */
    @Override
    public List<Long> getDeviceUsers(Long deviceId)
    {
        // 实现获取设备绑定用户的逻辑
        return new ArrayList<>();
    }

    /**
     * 检查设备是否可用
     *
     * @param deviceCode 设备编码
     * @param macAddress MAC地址
     * @return 结果
     */
    @Override
    public boolean checkDeviceAvailable(String deviceCode, String macAddress)
    {
        EmDevice queryDevice = new EmDevice();
        queryDevice.setDeviceCode(deviceCode);
        queryDevice.setMacAddress(macAddress);
        queryDevice.setStatus("0"); // 启用状态

        List<EmDevice> devices = emDeviceMapper.selectEmDeviceList(queryDevice);
        return !devices.isEmpty();
    }
}
