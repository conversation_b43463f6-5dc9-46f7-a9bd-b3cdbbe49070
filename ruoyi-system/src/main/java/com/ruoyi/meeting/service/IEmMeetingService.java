package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmMeeting;
import com.ruoyi.meeting.domain.EmMeetingParticipant;
import org.springframework.web.multipart.MultipartFile;

/**
 * 会议管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmMeetingService 
{
    /**
     * 查询会议
     * 
     * @param meetingId 会议主键
     * @return 会议
     */
    public EmMeeting selectEmMeetingByMeetingId(Long meetingId);

    /**
     * 查询会议列表
     * 
     * @param emMeeting 会议
     * @return 会议集合
     */
    public List<EmMeeting> selectEmMeetingList(EmMeeting emMeeting);

    /**
     * 新增会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    public int insertEmMeeting(EmMeeting emMeeting);

    /**
     * 修改会议
     * 
     * @param emMeeting 会议
     * @return 结果
     */
    public int updateEmMeeting(EmMeeting emMeeting);

    /**
     * 批量删除会议
     * 
     * @param meetingIds 需要删除的会议主键集合
     * @return 结果
     */
    public int deleteEmMeetingByMeetingIds(Long[] meetingIds);

    /**
     * 删除会议信息
     * 
     * @param meetingId 会议主键
     * @return 结果
     */
    public int deleteEmMeetingByMeetingId(Long meetingId);

    /**
     * 导入会议数据
     * 
     * @param file 会议数据文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importMeeting(MultipartFile file, Boolean updateSupport) throws Exception;

    /**
     * 下载会议导入模板
     * 
     * @return 模板文件路径
     */
    public String downloadTemplate();

    /**
     * 更新会议状态
     * 
     * @param meetingId 会议ID
     * @param status 状态
     * @return 结果
     */
    public int updateMeetingStatus(Long meetingId, String status);

    /**
     * 添加会议参与人员
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int addParticipants(Long meetingId, Long[] userIds);

    /**
     * 移除会议参与人员
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int removeParticipants(Long meetingId, Long[] userIds);

    /**
     * 获取会议参与人员
     * 
     * @param meetingId 会议ID
     * @return 参与人员列表
     */
    public List<EmMeetingParticipant> getMeetingParticipants(Long meetingId);

    /**
     * 设置会议参与人员
     * 
     * @param meetingId 会议ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int setParticipants(Long meetingId, Long[] userIds);

    /**
     * 复制上次会议参与人员
     * 
     * @param meetingId 会议ID
     * @param lastMeetingId 上次会议ID
     * @return 结果
     */
    public int copyLastMeetingParticipants(Long meetingId, Long lastMeetingId);

    /**
     * 查询用户参与的会议列表
     * 
     * @param userId 用户ID
     * @return 会议列表
     */
    public List<EmMeeting> selectMeetingsByUserId(Long userId);

    /**
     * 查询会议统计信息
     * 
     * @return 统计信息
     */
    public List<EmMeeting> selectMeetingStatistics();

    /**
     * 开始会议
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    public int startMeeting(Long meetingId);

    /**
     * 结束会议
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    public int endMeeting(Long meetingId);

    /**
     * 取消会议
     * 
     * @param meetingId 会议ID
     * @return 结果
     */
    public int cancelMeeting(Long meetingId);
}
