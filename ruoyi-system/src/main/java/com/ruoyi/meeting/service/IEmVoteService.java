package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmVote;

/**
 * 投票管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmVoteService 
{
    /**
     * 查询投票
     * 
     * @param voteId 投票主键
     * @return 投票
     */
    public EmVote selectEmVoteByVoteId(Long voteId);

    /**
     * 查询投票列表
     * 
     * @param emVote 投票
     * @return 投票集合
     */
    public List<EmVote> selectEmVoteList(EmVote emVote);

    /**
     * 新增投票
     * 
     * @param emVote 投票
     * @return 结果
     */
    public int insertEmVote(EmVote emVote);

    /**
     * 修改投票
     * 
     * @param emVote 投票
     * @return 结果
     */
    public int updateEmVote(EmVote emVote);

    /**
     * 批量删除投票
     * 
     * @param voteIds 需要删除的投票主键集合
     * @return 结果
     */
    public int deleteEmVoteByVoteIds(Long[] voteIds);

    /**
     * 删除投票信息
     * 
     * @param voteId 投票主键
     * @return 结果
     */
    public int deleteEmVoteByVoteId(Long voteId);

    /**
     * 更新投票状态
     *
     * @param voteId 投票ID
     * @param status 状态
     * @return 结果
     */
    public int updateVoteStatus(Long voteId, String status);

    /**
     * 自动创建投票
     *
     * @param subMeetingId 子会议ID
     * @return 结果
     */
    public int autoCreateVote(Long subMeetingId);

    /**
     * 关联人员
     *
     * @param voteId 投票ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int relateParticipants(Long voteId, Long[] userIds);

    /**
     * 查询投票记录
     *
     * @param emVote 查询条件
     * @return 投票记录
     */
    public List<EmVote> selectVoteRecords(EmVote emVote);

    /**
     * 补投
     *
     * @param emVote 投票信息
     * @return 结果
     */
    public int supplementVote(EmVote emVote);

    /**
     * 关联用户
     *
     * @param voteId 投票ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int associateUsers(Long voteId, Long[] userIds);

    /**
     * 取消关联
     *
     * @param voteId 投票ID
     * @return 结果
     */
    public int cancelAssociation(Long voteId);

    /**
     * 开始投票
     *
     * @param voteId 投票ID
     * @return 结果
     */
    public int startVote(Long voteId);

    /**
     * 结束投票
     *
     * @param voteId 投票ID
     * @return 结果
     */
    public int endVote(Long voteId);

    /**
     * 用户投票
     *
     * @param voteId 投票ID
     * @param userId 用户ID
     * @param voteOption 投票选项
     * @return 结果
     */
    public int userVote(Long voteId, Long userId, String voteOption);

    /**
     * 查询投票明细
     *
     * @param voteId 投票ID
     * @return 投票记录列表
     */
    public List<Object> selectVoteDetails(Long voteId);

    /**
     * 导出投票结果
     *
     * @param voteId 投票ID
     * @return 文件路径
     */
    public String exportVoteResult(Long voteId);
}
