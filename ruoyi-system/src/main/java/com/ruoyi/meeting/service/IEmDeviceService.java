package com.ruoyi.meeting.service;

import java.util.List;
import com.ruoyi.meeting.domain.EmDevice;

/**
 * 设备管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IEmDeviceService 
{
    /**
     * 查询设备
     * 
     * @param deviceId 设备主键
     * @return 设备
     */
    public EmDevice selectEmDeviceByDeviceId(Long deviceId);

    /**
     * 查询设备列表
     * 
     * @param emDevice 设备
     * @return 设备集合
     */
    public List<EmDevice> selectEmDeviceList(EmDevice emDevice);

    /**
     * 新增设备
     * 
     * @param emDevice 设备
     * @return 结果
     */
    public int insertEmDevice(EmDevice emDevice);

    /**
     * 修改设备
     * 
     * @param emDevice 设备
     * @return 结果
     */
    public int updateEmDevice(EmDevice emDevice);

    /**
     * 批量删除设备
     * 
     * @param deviceIds 需要删除的设备主键集合
     * @return 结果
     */
    public int deleteEmDeviceByDeviceIds(Long[] deviceIds);

    /**
     * 删除设备信息
     * 
     * @param deviceId 设备主键
     * @return 结果
     */
    public int deleteEmDeviceByDeviceId(Long deviceId);

    /**
     * 启用设备
     *
     * @param deviceId 设备ID
     * @return 结果
     */
    public int enableDevice(Long deviceId);

    /**
     * 禁用设备
     *
     * @param deviceId 设备ID
     * @return 结果
     */
    public int disableDevice(Long deviceId);

    /**
     * 绑定用户
     *
     * @param deviceId 设备ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int bindUsers(Long deviceId, Long[] userIds);

    /**
     * 解绑用户
     *
     * @param deviceId 设备ID
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int unbindUsers(Long deviceId, Long[] userIds);

    /**
     * 批量绑定用户
     *
     * @param deviceIds 设备ID数组
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int batchBindUsers(Long[] deviceIds, Long[] userIds);

    /**
     * 批量解绑用户
     *
     * @param deviceIds 设备ID数组
     * @param userIds 用户ID数组
     * @return 结果
     */
    public int batchUnbindUsers(Long[] deviceIds, Long[] userIds);

    /**
     * 获取设备绑定的用户
     *
     * @param deviceId 设备ID
     * @return 用户ID列表
     */
    public List<Long> getDeviceUsers(Long deviceId);

    /**
     * 检查设备是否可用
     *
     * @param deviceCode 设备编码
     * @param macAddress MAC地址
     * @return 结果
     */
    public boolean checkDeviceAvailable(String deviceCode, String macAddress);
}
