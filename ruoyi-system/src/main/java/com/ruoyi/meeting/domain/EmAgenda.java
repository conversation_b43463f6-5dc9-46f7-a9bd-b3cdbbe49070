package com.ruoyi.meeting.domain;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 议题对象 em_agenda
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmAgenda extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 议题ID */
    private Long agendaId;

    /** 子会议ID */
    @Excel(name = "子会议ID")
    private Long subMeetingId;

    /** 子会议名称 */
    @Excel(name = "子会议名称")
    private String subMeetingName;

    /** 议题标题 */
    @Excel(name = "议题标题")
    private String agendaTitle;

    /** 议题内容 */
    @Excel(name = "议题内容")
    private String agendaContent;

    /** 汇报人 */
    @Excel(name = "汇报人")
    private String presenter;

    /** 预计时长（分钟） */
    @Excel(name = "预计时长（分钟）")
    private Integer duration;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer orderNum;

    /** 状态（0待讨论 1讨论中 2已完成） */
    @Excel(name = "状态", readConverterExp = "0=待讨论,1=讨论中,2=已完成")
    private String status;

    /** 关联文件列表 */
    private List<EmFile> files;

    public void setAgendaId(Long agendaId) 
    {
        this.agendaId = agendaId;
    }

    public Long getAgendaId() 
    {
        return agendaId;
    }

    public void setSubMeetingId(Long subMeetingId) 
    {
        this.subMeetingId = subMeetingId;
    }

    public Long getSubMeetingId() 
    {
        return subMeetingId;
    }

    public void setSubMeetingName(String subMeetingName) 
    {
        this.subMeetingName = subMeetingName;
    }

    public String getSubMeetingName() 
    {
        return subMeetingName;
    }

    public void setAgendaTitle(String agendaTitle) 
    {
        this.agendaTitle = agendaTitle;
    }

    public String getAgendaTitle() 
    {
        return agendaTitle;
    }

    public void setAgendaContent(String agendaContent) 
    {
        this.agendaContent = agendaContent;
    }

    public String getAgendaContent() 
    {
        return agendaContent;
    }

    public void setPresenter(String presenter) 
    {
        this.presenter = presenter;
    }

    public String getPresenter() 
    {
        return presenter;
    }

    public void setDuration(Integer duration) 
    {
        this.duration = duration;
    }

    public Integer getDuration() 
    {
        return duration;
    }

    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<EmFile> getFiles() 
    {
        return files;
    }

    public void setFiles(List<EmFile> files) 
    {
        this.files = files;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("agendaId", getAgendaId())
            .append("subMeetingId", getSubMeetingId())
            .append("agendaTitle", getAgendaTitle())
            .append("agendaContent", getAgendaContent())
            .append("presenter", getPresenter())
            .append("duration", getDuration())
            .append("orderNum", getOrderNum())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
