package com.ruoyi.meeting.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文件分发记录对象 em_file_distribution
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmFileDistribution extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分发记录ID */
    private Long distributionId;

    /** 文件ID */
    @Excel(name = "文件ID")
    private Long fileId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 分发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date distributionTime;

    /** 阅读时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /** 下载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date downloadTime;

    /** 状态（0已分发 1已阅读 2已下载 3已收回） */
    @Excel(name = "状态", readConverterExp = "0=已分发,1=已阅读,2=已下载,3=已收回")
    private String status;

    public void setDistributionId(Long distributionId) 
    {
        this.distributionId = distributionId;
    }

    public Long getDistributionId() 
    {
        return distributionId;
    }

    public void setFileId(Long fileId) 
    {
        this.fileId = fileId;
    }

    public Long getFileId() 
    {
        return fileId;
    }

    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }

    public void setDistributionTime(Date distributionTime) 
    {
        this.distributionTime = distributionTime;
    }

    public Date getDistributionTime() 
    {
        return distributionTime;
    }

    public void setReadTime(Date readTime) 
    {
        this.readTime = readTime;
    }

    public Date getReadTime() 
    {
        return readTime;
    }

    public void setDownloadTime(Date downloadTime) 
    {
        this.downloadTime = downloadTime;
    }

    public Date getDownloadTime() 
    {
        return downloadTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("distributionId", getDistributionId())
            .append("fileId", getFileId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("distributionTime", getDistributionTime())
            .append("readTime", getReadTime())
            .append("downloadTime", getDownloadTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
