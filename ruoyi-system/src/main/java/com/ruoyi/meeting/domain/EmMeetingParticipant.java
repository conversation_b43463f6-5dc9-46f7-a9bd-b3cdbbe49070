package com.ruoyi.meeting.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会议参与人员对象 em_meeting_participant
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class EmMeetingParticipant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 参与人员ID */
    private Long participantId;

    /** 会议ID */
    @Excel(name = "会议ID")
    private Long meetingId;

    /** 子会议ID */
    @Excel(name = "子会议ID")
    private Long subMeetingId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 角色类型（0参会人员 1主持人 2记录员） */
    @Excel(name = "角色类型", readConverterExp = "0=参会人员,1=主持人,2=记录员")
    private String roleType;

    /** 状态（0正常 1请假 2缺席） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=请假,2=缺席")
    private String status;

    public void setParticipantId(Long participantId) 
    {
        this.participantId = participantId;
    }

    public Long getParticipantId() 
    {
        return participantId;
    }

    public void setMeetingId(Long meetingId) 
    {
        this.meetingId = meetingId;
    }

    public Long getMeetingId() 
    {
        return meetingId;
    }

    public void setSubMeetingId(Long subMeetingId) 
    {
        this.subMeetingId = subMeetingId;
    }

    public Long getSubMeetingId() 
    {
        return subMeetingId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }

    public void setRoleType(String roleType) 
    {
        this.roleType = roleType;
    }

    public String getRoleType() 
    {
        return roleType;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("participantId", getParticipantId())
            .append("meetingId", getMeetingId())
            .append("subMeetingId", getSubMeetingId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("roleType", getRoleType())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
