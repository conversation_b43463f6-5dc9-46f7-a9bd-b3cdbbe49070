package com.ruoyi.web.controller.meeting;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.meeting.domain.EmMeeting;
import com.ruoyi.meeting.domain.EmMeetingParticipant;
import com.ruoyi.meeting.service.IEmMeetingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会议管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/meeting/meeting")
public class EmMeetingController extends BaseController
{
    @Autowired
    private IEmMeetingService emMeetingService;

    /**
     * 查询会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmMeeting emMeeting)
    {
        startPage();
        List<EmMeeting> list = emMeetingService.selectEmMeetingList(emMeeting);
        return getDataTable(list);
    }

    /**
     * 导出会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:export')")
    @Log(title = "会议管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmMeeting emMeeting)
    {
        List<EmMeeting> list = emMeetingService.selectEmMeetingList(emMeeting);
        ExcelUtil<EmMeeting> util = new ExcelUtil<EmMeeting>(EmMeeting.class);
        util.exportExcel(response, list, "会议数据");
    }

    /**
     * 获取会议详细信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:query')")
    @GetMapping(value = "/{meetingId}")
    public AjaxResult getInfo(@PathVariable("meetingId") Long meetingId)
    {
        return success(emMeetingService.selectEmMeetingByMeetingId(meetingId));
    }

    /**
     * 新增会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:add')")
    @Log(title = "会议管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmMeeting emMeeting)
    {
        return toAjax(emMeetingService.insertEmMeeting(emMeeting));
    }

    /**
     * 修改会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmMeeting emMeeting)
    {
        return toAjax(emMeetingService.updateEmMeeting(emMeeting));
    }

    /**
     * 删除会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:remove')")
    @Log(title = "会议管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{meetingIds}")
    public AjaxResult remove(@PathVariable Long[] meetingIds)
    {
        return toAjax(emMeetingService.deleteEmMeetingByMeetingIds(meetingIds));
    }

    /**
     * 导入会议数据
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:import')")
    @Log(title = "会议管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        String message = emMeetingService.importMeeting(file, updateSupport);
        return success(message);
    }

    /**
     * 下载会议导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        String templatePath = emMeetingService.downloadTemplate();
        // 这里实现文件下载逻辑
    }

    /**
     * 更新会议状态
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestParam Long meetingId, @RequestParam String status)
    {
        return toAjax(emMeetingService.updateMeetingStatus(meetingId, status));
    }

    /**
     * 开始会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping("/start/{meetingId}")
    public AjaxResult startMeeting(@PathVariable Long meetingId)
    {
        return toAjax(emMeetingService.startMeeting(meetingId));
    }

    /**
     * 结束会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping("/end/{meetingId}")
    public AjaxResult endMeeting(@PathVariable Long meetingId)
    {
        return toAjax(emMeetingService.endMeeting(meetingId));
    }

    /**
     * 取消会议
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{meetingId}")
    public AjaxResult cancelMeeting(@PathVariable Long meetingId)
    {
        return toAjax(emMeetingService.cancelMeeting(meetingId));
    }

    /**
     * 获取会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:query')")
    @GetMapping("/participants/{meetingId}")
    public AjaxResult getParticipants(@PathVariable Long meetingId)
    {
        List<EmMeetingParticipant> participants = emMeetingService.getMeetingParticipants(meetingId);
        return success(participants);
    }

    /**
     * 设置会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/{meetingId}")
    public AjaxResult setParticipants(@PathVariable Long meetingId, @RequestBody Long[] userIds)
    {
        return toAjax(emMeetingService.setParticipants(meetingId, userIds));
    }

    /**
     * 添加会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/{meetingId}/add")
    public AjaxResult addParticipants(@PathVariable Long meetingId, @RequestBody Long[] userIds)
    {
        return toAjax(emMeetingService.addParticipants(meetingId, userIds));
    }

    /**
     * 移除会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @DeleteMapping("/participants/{meetingId}")
    public AjaxResult removeParticipants(@PathVariable Long meetingId, @RequestBody Long[] userIds)
    {
        return toAjax(emMeetingService.removeParticipants(meetingId, userIds));
    }

    /**
     * 复制上次会议参与人员
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/participants/{meetingId}/copy/{lastMeetingId}")
    public AjaxResult copyLastMeetingParticipants(@PathVariable Long meetingId, @PathVariable Long lastMeetingId)
    {
        return toAjax(emMeetingService.copyLastMeetingParticipants(meetingId, lastMeetingId));
    }

    /**
     * 查询用户参与的会议列表
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:list')")
    @GetMapping("/user/{userId}")
    public TableDataInfo getMeetingsByUserId(@PathVariable Long userId)
    {
        startPage();
        List<EmMeeting> list = emMeetingService.selectMeetingsByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 查询会议统计信息
     */
    @PreAuthorize("@ss.hasPermi('meeting:meeting:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        List<EmMeeting> statistics = emMeetingService.selectMeetingStatistics();
        return success(statistics);
    }
}
