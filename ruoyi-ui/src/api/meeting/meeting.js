import request from '@/utils/request'

// 查询会议列表
export function listMeeting(query) {
  return request({
    url: '/meeting/meeting/list',
    method: 'get',
    params: query
  })
}

// 查询会议详细
export function getMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/' + meetingId,
    method: 'get'
  })
}

// 新增会议
export function addMeeting(data) {
  return request({
    url: '/meeting/meeting',
    method: 'post',
    data: data
  })
}

// 修改会议
export function updateMeeting(data) {
  return request({
    url: '/meeting/meeting',
    method: 'put',
    data: data
  })
}

// 删除会议
export function delMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/' + meetingId,
    method: 'delete'
  })
}

// 更新会议状态
export function updateMeetingStatus(meetingId, status) {
  return request({
    url: '/meeting/meeting/status',
    method: 'put',
    params: {
      meetingId: meetingId,
      status: status
    }
  })
}

// 开始会议
export function startMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/start/' + meetingId,
    method: 'put'
  })
}

// 结束会议
export function endMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/end/' + meetingId,
    method: 'put'
  })
}

// 取消会议
export function cancelMeeting(meetingId) {
  return request({
    url: '/meeting/meeting/cancel/' + meetingId,
    method: 'put'
  })
}

// 获取会议参与人员
export function getMeetingParticipants(meetingId) {
  return request({
    url: '/meeting/meeting/participants/' + meetingId,
    method: 'get'
  })
}

// 设置会议参与人员
export function setMeetingParticipants(meetingId, userIds) {
  return request({
    url: '/meeting/meeting/participants/' + meetingId,
    method: 'post',
    data: userIds
  })
}

// 添加会议参与人员
export function addMeetingParticipants(meetingId, userIds) {
  return request({
    url: '/meeting/meeting/participants/' + meetingId + '/add',
    method: 'post',
    data: userIds
  })
}

// 移除会议参与人员
export function removeMeetingParticipants(meetingId, userIds) {
  return request({
    url: '/meeting/meeting/participants/' + meetingId,
    method: 'delete',
    data: userIds
  })
}

// 复制上次会议参与人员
export function copyLastMeetingParticipants(meetingId, lastMeetingId) {
  return request({
    url: '/meeting/meeting/participants/' + meetingId + '/copy/' + lastMeetingId,
    method: 'post'
  })
}

// 查询用户参与的会议列表
export function getMeetingsByUserId(userId, query) {
  return request({
    url: '/meeting/meeting/user/' + userId,
    method: 'get',
    params: query
  })
}

// 查询会议统计信息
export function getMeetingStatistics() {
  return request({
    url: '/meeting/meeting/statistics',
    method: 'get'
  })
}

// 导出会议
export function exportMeeting(query) {
  return request({
    url: '/meeting/meeting/export',
    method: 'post',
    data: query
  })
}
