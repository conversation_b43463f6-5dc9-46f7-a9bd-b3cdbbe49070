import request from '@/utils/request'

// 查询会议类型列表
export function listMeetingType(query) {
  return request({
    url: '/meeting/type/list',
    method: 'get',
    params: query
  })
}

// 查询会议类型树结构
export function getMeetingTypeTree(query) {
  return request({
    url: '/meeting/type/tree',
    method: 'get',
    params: query
  })
}

// 查询会议类型详细
export function getMeetingType(typeId) {
  return request({
    url: '/meeting/type/' + typeId,
    method: 'get'
  })
}

// 新增会议类型
export function addMeetingType(data) {
  return request({
    url: '/meeting/type',
    method: 'post',
    data: data
  })
}

// 修改会议类型
export function updateMeetingType(data) {
  return request({
    url: '/meeting/type',
    method: 'put',
    data: data
  })
}

// 删除会议类型
export function delMeetingType(typeId) {
  return request({
    url: '/meeting/type/' + typeId,
    method: 'delete'
  })
}

// 查询会议类型下拉树结构
export function treeselect() {
  return request({
    url: '/meeting/type/treeselect',
    method: 'get'
  })
}
