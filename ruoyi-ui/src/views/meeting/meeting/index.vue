<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会议名称" prop="meetingName">
        <el-input
          v-model="queryParams.meetingName"
          placeholder="请输入会议名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会议类型" prop="typeId">
        <el-select v-model="queryParams.typeId" placeholder="请选择会议类型" clearable>
          <el-option
            v-for="type in meetingTypeOptions"
            :key="type.typeId"
            :label="type.typeName"
            :value="type.typeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="未开始" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已结束" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['meeting:meeting:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['meeting:meeting:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['meeting:meeting:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['meeting:meeting:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['meeting:meeting:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="meetingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会议ID" align="center" prop="meetingId" />
      <el-table-column label="会议名称" align="center" prop="meetingName" :show-overflow-tooltip="true" />
      <el-table-column label="会议类型" align="center" prop="typeName" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议地点" align="center" prop="location" :show-overflow-tooltip="true" />
      <el-table-column label="组织者" align="center" prop="organizer" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.meeting_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['meeting:meeting:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleParticipants(scope.row)"
            v-hasPermi="['meeting:meeting:edit']"
          >人员管理</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['meeting:meeting:edit']">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="start" icon="el-icon-video-play" v-if="scope.row.status === '0'">开始会议</el-dropdown-item>
              <el-dropdown-item command="end" icon="el-icon-video-pause" v-if="scope.row.status === '1'">结束会议</el-dropdown-item>
              <el-dropdown-item command="cancel" icon="el-icon-close" v-if="scope.row.status === '0' || scope.row.status === '1'">取消会议</el-dropdown-item>
              <el-dropdown-item command="delete" icon="el-icon-delete" v-hasPermi="['meeting:meeting:remove']">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会议对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="会议名称" prop="meetingName">
              <el-input v-model="form.meetingName" placeholder="请输入会议名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议类型" prop="typeId">
              <el-select v-model="form.typeId" placeholder="请选择会议类型">
                <el-option
                  v-for="type in meetingTypeOptions"
                  :key="type.typeId"
                  :label="type.typeName"
                  :value="type.typeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="会议地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入会议地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织者" prop="organizer">
              <el-input v-model="form.organizer" placeholder="请输入组织者" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="会议描述" prop="meetingDesc">
          <el-input v-model="form.meetingDesc" type="textarea" placeholder="请输入会议描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 会议导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".zip"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的会议数据
          </div>
          <span>仅允许导入zip格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMeeting, getMeeting, delMeeting, addMeeting, updateMeeting } from "@/api/meeting/meeting";
import { listMeetingType } from "@/api/meeting/type";

export default {
  name: "Meeting",
  dicts: ['meeting_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会议表格数据
      meetingList: [],
      // 会议类型选项
      meetingTypeOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        meetingName: null,
        typeId: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        meetingName: [
          { required: true, message: "会议名称不能为空", trigger: "blur" }
        ],
        typeId: [
          { required: true, message: "会议类型不能为空", trigger: "change" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ]
      },
      // 会议导入参数
      upload: {
        // 是否显示弹出层（会议导入）
        open: false,
        // 弹出层标题（会议导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的会议数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/meeting/meeting/importData"
      }
    };
  },
  created() {
    this.getList();
    this.getMeetingTypeOptions();
  },
  methods: {
    /** 查询会议列表 */
    getList() {
      this.loading = true;
      listMeeting(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.meetingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询会议类型选项 */
    getMeetingTypeOptions() {
      listMeetingType().then(response => {
        this.meetingTypeOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        meetingId: null,
        meetingName: null,
        meetingDesc: null,
        typeId: null,
        startTime: null,
        endTime: null,
        location: null,
        organizer: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.meetingId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会议";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const meetingId = row.meetingId || this.ids
      getMeeting(meetingId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会议";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.meetingId != null) {
            updateMeeting(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeeting(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const meetingIds = row.meetingId || this.ids;
      this.$modal.confirm('是否确认删除会议编号为"' + meetingIds + '"的数据项？').then(function() {
        return delMeeting(meetingIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('meeting/meeting/export', {
        ...this.queryParams
      }, `meeting_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "会议导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('meeting/meeting/importTemplate', {
      }, `meeting_template_${new Date().getTime()}.zip`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 人员管理 */
    handleParticipants(row) {
      this.$router.push({
        path: '/meeting/participants',
        query: { meetingId: row.meetingId, meetingName: row.meetingName }
      });
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "start":
          this.startMeeting(row);
          break;
        case "end":
          this.endMeeting(row);
          break;
        case "cancel":
          this.cancelMeeting(row);
          break;
        case "delete":
          this.handleDelete(row);
          break;
      }
    },
    /** 开始会议 */
    startMeeting(row) {
      this.$modal.confirm('确认开始会议"' + row.meetingName + '"？').then(() => {
        return this.$http.put(`/meeting/meeting/start/${row.meetingId}`);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("会议已开始");
      }).catch(() => {});
    },
    /** 结束会议 */
    endMeeting(row) {
      this.$modal.confirm('确认结束会议"' + row.meetingName + '"？').then(() => {
        return this.$http.put(`/meeting/meeting/end/${row.meetingId}`);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("会议已结束");
      }).catch(() => {});
    },
    /** 取消会议 */
    cancelMeeting(row) {
      this.$modal.confirm('确认取消会议"' + row.meetingName + '"？').then(() => {
        return this.$http.put(`/meeting/meeting/cancel/${row.meetingId}`);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("会议已取消");
      }).catch(() => {});
    }
  }
};
</script>
